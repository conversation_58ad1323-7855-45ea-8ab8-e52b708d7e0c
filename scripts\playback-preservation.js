// Global Playback Preservation System
// This script ensures uninterrupted music playback across all page navigation and interactions

(function () {
  "use strict";

  // Enhanced Global playback preservation manager for completely uninterrupted playback
  window.PlaybackPreservation = {
    isInitialized: false,
    preservationActive: false,

    init: function () {
      if (this.isInitialized) return;

      console.log("Initializing ENHANCED Playback Preservation System");

      // Preserve playback on page unload with maximum priority
      window.addEventListener("beforeunload", this.preserveOnUnload.bind(this));

      // Preserve playback on page visibility change
      document.addEventListener(
        "visibilitychange",
        this.handleVisibilityChange.bind(this)
      );

      // Preserve playback on focus/blur
      window.addEventListener("blur", this.preserveOnBlur.bind(this));
      window.addEventListener("focus", this.restoreOnFocus.bind(this));

      // Override navigation methods to preserve playback
      this.overrideNavigation();

      // Add additional protection mechanisms
      this.setupAdditionalProtection();

      this.isInitialized = true;
    },

    preserveOnUnload: function () {
      if (window.GlobalAudioManager) {
        const state = window.GlobalAudioManager.preservePlayback();
        console.log(
          "MAXIMUM PRIORITY: Preserving playback on page unload:",
          state
        );

        // Force continuation if playing
        if (state.wasPlaying) {
          this.preservationActive = true;
          window.GlobalAudioManager.forcePlaybackContinuation();
        }
      }
    },

    handleVisibilityChange: function () {
      if (document.hidden) {
        // Page is hidden, preserve state with maximum protection
        if (window.GlobalAudioManager) {
          window.GlobalAudioManager.preservePlayback();
          this.preservationActive = true;
        }
      } else {
        // Page is visible again, ensure playback continues
        this.ensurePlaybackContinuity();
      }
    },

    preserveOnBlur: function () {
      if (window.GlobalAudioManager) {
        window.GlobalAudioManager.preservePlayback();
        this.preservationActive = true;
      }
    },

    restoreOnFocus: function () {
      this.ensurePlaybackContinuity();
    },

    // Setup additional protection mechanisms
    setupAdditionalProtection: function () {
      // Prevent page refresh from interrupting playback
      window.addEventListener("beforeunload", (e) => {
        if (
          window.GlobalAudioManager &&
          window.GlobalAudioManager.isPlaybackProtected()
        ) {
          // Try to prevent page unload if music is playing
          e.preventDefault();
          e.returnValue = "";
          window.GlobalAudioManager.forcePlaybackContinuation();
        }
      });

      // Monitor for any attempts to pause the audio
      if (window.GlobalAudioManager) {
        const music = window.GlobalAudioManager.getInstance();
        if (music) {
          // Override pause method to prevent unwanted pauses
          const originalPause = music.pause.bind(music);
          music.pause = function () {
            if (window.GlobalAudioManager.isPlaybackProtected()) {
              console.log("BLOCKED: Attempt to pause protected playback");
              return;
            }
            return originalPause();
          };
        }
      }
    },

    ensurePlaybackContinuity: function () {
      if (!window.GlobalAudioManager) return;

      const music = window.GlobalAudioManager.getInstance();
      if (!music) return;

      // Check if playback is protected (should be playing)
      const isProtected = window.GlobalAudioManager.isPlaybackProtected();
      const state = getCurrentPlayerState ? getCurrentPlayerState() : null;

      if (isProtected || (state && state.isPlaying && music.paused)) {
        console.log("Ensuring playback continuity - attempting to resume");

        // Try to resume playback with multiple attempts
        let attempts = 0;
        const maxAttempts = 3;

        const attemptResume = () => {
          attempts++;
          music
            .play()
            .then(() => {
              console.log("Successfully resumed playback after interruption");

              // Update UI to reflect playing state
              const masterPlay = document.getElementById("masterPlay");
              const wave = document.querySelector(".wave");

              if (masterPlay) {
                masterPlay.classList.remove("bi-play-fill");
                masterPlay.classList.add("bi-pause-fill");
              }

              if (wave) {
                wave.classList.add("active2");
              }
            })
            .catch((error) => {
              console.log(`Resume attempt ${attempts} failed:`, error);

              if (attempts < maxAttempts) {
                // Try again after a short delay
                setTimeout(attemptResume, 500);
              } else {
                console.log("All resume attempts failed - clearing protection");
                if (window.GlobalAudioManager) {
                  window.GlobalAudioManager.clearPlaybackProtection();
                }
              }
            });
        };

        attemptResume();
      }
    },

    overrideNavigation: function () {
      // Enhanced navigation override for maximum playback protection

      // Override window.location.href assignments
      const originalLocationSetter =
        Object.getOwnPropertyDescriptor(window.location, "href") ||
        Object.getOwnPropertyDescriptor(Location.prototype, "href");

      if (originalLocationSetter && originalLocationSetter.set) {
        Object.defineProperty(window.location, "href", {
          set: function (url) {
            // MAXIMUM PRIORITY: Preserve playback before navigation
            if (window.GlobalAudioManager) {
              console.log("NAVIGATION DETECTED: Preserving playback for", url);
              window.GlobalAudioManager.preservePlayback();

              // Force continuation if protected
              if (window.GlobalAudioManager.isPlaybackProtected()) {
                setTimeout(() => {
                  window.GlobalAudioManager.forcePlaybackContinuation();
                }, 100);
              }
            }
            originalLocationSetter.set.call(this, url);
          },
          get: originalLocationSetter.get,
        });
      }

      // Override history navigation
      const originalPushState = history.pushState;
      const originalReplaceState = history.replaceState;

      history.pushState = function () {
        if (window.GlobalAudioManager) {
          console.log("HISTORY PUSH: Preserving playback");
          window.GlobalAudioManager.preservePlayback();
        }
        return originalPushState.apply(this, arguments);
      };

      history.replaceState = function () {
        if (window.GlobalAudioManager) {
          console.log("HISTORY REPLACE: Preserving playback");
          window.GlobalAudioManager.preservePlayback();
        }
        return originalReplaceState.apply(this, arguments);
      };

      // Enhanced link click override with maximum protection
      document.addEventListener(
        "click",
        function (e) {
          const link = e.target.closest("a[href]");
          if (
            link &&
            !link.href.startsWith("javascript:") &&
            !link.href.startsWith("#") &&
            !link.href.startsWith("mailto:")
          ) {
            // This is a navigation link, activate maximum protection
            console.log("LINK NAVIGATION DETECTED:", link.href);

            if (window.GlobalAudioManager) {
              window.GlobalAudioManager.preservePlayback();

              // If playback is protected, ensure it continues
              if (window.GlobalAudioManager.isPlaybackProtected()) {
                // Multiple recovery attempts during navigation
                setTimeout(
                  () => window.GlobalAudioManager.forcePlaybackContinuation(),
                  50
                );
                setTimeout(
                  () => window.GlobalAudioManager.forcePlaybackContinuation(),
                  200
                );
                setTimeout(
                  () => window.GlobalAudioManager.forcePlaybackContinuation(),
                  500
                );
                setTimeout(
                  () => window.GlobalAudioManager.forcePlaybackContinuation(),
                  1000
                );
              }
            }
          }
        },
        true
      );

      // Override form submissions that might cause navigation
      document.addEventListener(
        "submit",
        function (e) {
          if (window.GlobalAudioManager) {
            console.log("FORM SUBMISSION: Preserving playback");
            window.GlobalAudioManager.preservePlayback();
          }
        },
        true
      );

      // Override browser back/forward buttons
      window.addEventListener("popstate", function (e) {
        if (window.GlobalAudioManager) {
          console.log("BROWSER NAVIGATION: Preserving playback");
          window.GlobalAudioManager.preservePlayback();

          // Ensure continuation after navigation
          setTimeout(() => {
            if (window.GlobalAudioManager.isPlaybackProtected()) {
              window.GlobalAudioManager.forcePlaybackContinuation();
            }
          }, 100);
        }
      });
    },

    // Method to ensure audio element is not recreated
    protectAudioElement: function () {
      if (!window.GlobalAudioManager) return;

      const music = window.GlobalAudioManager.getInstance();
      if (!music) return;

      // Prevent audio element from being garbage collected
      window._protectedAudio = music;

      // Add error recovery
      music.addEventListener("error", function (e) {
        console.error("Audio error occurred:", e);
        // Try to recover by reloading the current source
        const currentSrc = music.src;
        if (currentSrc) {
          setTimeout(() => {
            music.src = currentSrc;
            music.load();
          }, 1000);
        }
      });

      // Add stalled event handler
      music.addEventListener("stalled", function () {
        console.log("Audio stalled, attempting recovery");
        music.load();
      });

      // Start continuous monitoring for playback interruptions
      this.startPlaybackMonitoring();
    },

    // Continuous monitoring to prevent playback interruptions
    startPlaybackMonitoring: function () {
      if (this.monitoringInterval) {
        clearInterval(this.monitoringInterval);
      }

      this.monitoringInterval = setInterval(() => {
        if (!window.GlobalAudioManager) return;

        const music = window.GlobalAudioManager.getInstance();
        const isProtected = window.GlobalAudioManager.isPlaybackProtected();

        // If playback is protected but audio is paused, try to resume
        if (isProtected && music && music.paused) {
          console.log("Monitoring detected interruption - attempting recovery");
          this.ensurePlaybackContinuity();
        }
      }, 1000); // Check every second
    },

    // Stop monitoring (call when cleaning up)
    stopPlaybackMonitoring: function () {
      if (this.monitoringInterval) {
        clearInterval(this.monitoringInterval);
        this.monitoringInterval = null;
      }
    },
  };

  // Initialize when DOM is ready
  if (document.readyState === "loading") {
    document.addEventListener("DOMContentLoaded", function () {
      window.PlaybackPreservation.init();
      window.PlaybackPreservation.protectAudioElement();
    });
  } else {
    window.PlaybackPreservation.init();
    window.PlaybackPreservation.protectAudioElement();
  }

  // Also initialize immediately if GlobalAudioManager is available
  if (window.GlobalAudioManager) {
    window.PlaybackPreservation.protectAudioElement();
  }
})();

// Export for manual initialization if needed
window.initializePlaybackPreservation = function () {
  if (window.PlaybackPreservation) {
    window.PlaybackPreservation.init();
    window.PlaybackPreservation.protectAudioElement();
  }
};
