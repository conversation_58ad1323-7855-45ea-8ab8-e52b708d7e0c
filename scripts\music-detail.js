// Music Detail Page JavaScript

document.addEventListener("DOMContentLoaded", () => {
  // Preserve any ongoing playback before loading new state
  if (window.GlobalAudioManager) {
    const playbackState = window.GlobalAudioManager.preservePlayback();
    console.log("Preserved playback state:", playbackState);

    // If music was playing, ensure it continues
    if (playbackState.wasPlaying) {
      console.log(
        "Music was playing - ensuring continuity on music detail page"
      );
    }
  }

  // Load player state first - this will now respect ongoing playback
  if (typeof loadPlayerState === "function") {
    loadPlayerState();
  }

  // Get song ID and source from URL parameters
  const urlParams = new URLSearchParams(window.location.search);
  const songId = urlParams.get("id");
  const source = urlParams.get("source") || "discovery";

  if (!songId) {
    // If no song ID is provided, redirect to home page
    window.location.href = "index.html";
    return;
  }

  // Find the song in the songs array
  const song = songs.find((song) => song.id === songId);

  if (!song) {
    // If song not found, show error message
    document.querySelector(".music-detail-container").innerHTML = `
            <div class="error-message">
                <h2>Bài hát không tồn tại</h2>
                <p>Bài hát bạn đang tìm kiếm không tồn tại hoặc đã bị xóa.</p>
                <a href="index.html" class="action-btn">
                    <i class="bi bi-house-door"></i> Về trang chủ
                </a>
            </div>
        `;
    return;
  }

  // Update page title
  document.title = `${extractSongName(song.songName)} - Music App`;

  // Update contextual navigation and styling
  updateContextualNavigation(source);

  // Update song details
  updateSongDetails(song);

  // Load related songs
  loadRelatedSongs(song);

  // Add event listeners
  setupEventListeners(song, source);
});

// Update contextual navigation based on source
function updateContextualNavigation(source) {
  const backButton = document.querySelector(".back-button a");
  const breadcrumbSource = document.querySelector(".breadcrumb-source");
  const musicDetailContainer = document.querySelector(
    ".music-detail-container"
  );

  // Define source configurations
  const sourceConfig = {
    library: {
      backUrl: "library.html",
      breadcrumbText: "Thư Viện Của Tôi",
      contextClass: "library-context",
      navId: "nav-library",
      sidebarId: "sidebar-library",
    },
    "recently-played": {
      backUrl: "recently-played.html",
      breadcrumbText: "Gần Đây",
      contextClass: "recently-played-context",
      navId: "nav-recently-played",
      sidebarId: "sidebar-recently-played",
    },
    discovery: {
      backUrl: "index.html",
      breadcrumbText: "Khám Phá",
      contextClass: "discovery-context",
      navId: "nav-discovery",
      sidebarId: "sidebar-discovery",
    },
    search: {
      backUrl: "index.html",
      breadcrumbText: "Tìm Kiếm",
      contextClass: "search-context",
      navId: "nav-discovery",
      sidebarId: "sidebar-discovery",
    },
  };

  const config = sourceConfig[source] || sourceConfig.discovery;

  // Update back button
  if (backButton) {
    backButton.href = config.backUrl;
  }

  // Update breadcrumb
  if (breadcrumbSource) {
    breadcrumbSource.textContent = config.breadcrumbText;
  }

  // Add contextual CSS class
  if (musicDetailContainer) {
    // Remove existing context classes
    Object.values(sourceConfig).forEach((cfg) => {
      musicDetailContainer.classList.remove(cfg.contextClass);
    });
    // Add current context class
    musicDetailContainer.classList.add(config.contextClass);
  }

  // Clear all active states first
  const allNavItems = document.querySelectorAll("nav ul li");
  const allSidebarItems = document.querySelectorAll(".playlist h4");

  allNavItems.forEach((item) => {
    item.classList.remove("active");
    const span = item.querySelector("span");
    if (span) span.remove();
  });

  allSidebarItems.forEach((item) => {
    item.classList.remove("active");
    const span = item.querySelector("span");
    if (span) span.remove();
  });

  // Set active states based on source
  const activeNavItem = document.getElementById(config.navId);
  const activeSidebarItem = document.getElementById(config.sidebarId);

  if (activeNavItem) {
    activeNavItem.classList.add("active");
    if (!activeNavItem.querySelector("span")) {
      activeNavItem.innerHTML += "<span></span>";
    }
  }

  if (activeSidebarItem) {
    activeSidebarItem.classList.add("active");
    if (!activeSidebarItem.querySelector("span")) {
      activeSidebarItem.innerHTML += "<span></span>";
    }
  }
}

// Extract song name from HTML string
function extractSongName(songNameHtml) {
  // Create a temporary div to parse the HTML
  const tempDiv = document.createElement("div");
  tempDiv.innerHTML = songNameHtml;

  // Get the text content (song name) without the subtitle
  const songName = tempDiv.childNodes[0].textContent.trim();
  return songName;
}

// Extract artist name from HTML string
function extractArtistName(songNameHtml) {
  // Create a temporary div to parse the HTML
  const tempDiv = document.createElement("div");
  tempDiv.innerHTML = songNameHtml;

  // Get the subtitle div content (artist name)
  const artistElement = tempDiv.querySelector(".subtitle");
  return artistElement ? artistElement.textContent.trim() : "Unknown Artist";
}

// Update song details in the UI
function updateSongDetails(song) {
  // Update song poster
  document.getElementById("detail-poster").src = song.poster;

  // Update song title and artist
  const songName = extractSongName(song.songName);
  const artistName = extractArtistName(song.songName);

  document.getElementById("detail-title").textContent = songName;
  document.getElementById("detail-artist").textContent = artistName;

  // Update download button
  const downloadBtn = document.getElementById("download-btn");
  downloadBtn.addEventListener("click", () => {
    // Check if user is logged in
    if (typeof isUserLoggedIn === "function" && !isUserLoggedIn()) {
      if (typeof showLoginRequiredNotification === "function") {
        showLoginRequiredNotification();
      }
      return;
    }

    const downloadLink = document.createElement("a");
    downloadLink.href = `./audio/${song.id}.mp3`;
    downloadLink.download = songName;
    downloadLink.click();
  });

  // Add some dummy description and lyrics for demonstration
  document.getElementById(
    "detail-description"
  ).textContent = `"${songName}" là một bài hát của ${artistName}. Bài hát này đã được phát hành và nhận được nhiều sự yêu thích từ người nghe. Với giai điệu cuốn hút và lời bài hát ý nghĩa, "${songName}" đã trở thành một trong những bài hát nổi bật của ${artistName}.`;

  document.getElementById(
    "detail-lyrics"
  ).textContent = `Đây là phần lời bài hát của "${songName}". Hiện tại chúng tôi chưa có lời bài hát này. Vui lòng quay lại sau.`;
}

// Load related songs
function loadRelatedSongs(currentSong) {
  const relatedSongsContainer = document.querySelector(
    ".related-songs-container"
  );

  // Get 4 random songs that are not the current song
  const relatedSongs = songs
    .filter((song) => song.id !== currentSong.id)
    .sort(() => 0.5 - Math.random())
    .slice(0, 4);

  // Add related songs to the container
  relatedSongsContainer.innerHTML = "";

  relatedSongs.forEach((song) => {
    const songElement = document.createElement("div");
    songElement.className = "related-song-item";
    songElement.innerHTML = `
            <img src="${song.poster}" alt="Related Song">
            <h5>${extractSongName(song.songName)}</h5>
            <div class="subtitle">${extractArtistName(song.songName)}</div>
        `;

    // Add click event to navigate to the song detail page
    songElement.addEventListener("click", () => {
      // Get current source from URL to maintain context
      const urlParams = new URLSearchParams(window.location.search);
      const currentSource = urlParams.get("source") || "discovery";
      window.location.href = `music-detail.html?id=${song.id}&source=${currentSource}`;
    });

    relatedSongsContainer.appendChild(songElement);
  });
}

// Setup event listeners
function setupEventListeners(song, source) {
  // Play button in the detail page
  const detailPlayBtn = document.getElementById("detail-play-btn");
  const playBtn = document.getElementById("play-btn");

  const playCurrentSong = () => {
    // Use the context system to play the song
    if (window.MasterPlayerContext) {
      window.MasterPlayerContext.playContextualSong(song);
    } else {
      // Fallback to direct play if context system not available
      // Set the current song index
      index = parseInt(song.id);

      // Get the global audio instance to ensure continuity
      const music = window.GlobalAudioManager
        ? window.GlobalAudioManager.getInstance()
        : window.music;

      if (!music) return;

      // Update the music player
      music.src = `./audio/${index}.mp3`;

      const poster_master_play = document.getElementById("poster_master_play");
      if (poster_master_play) {
        poster_master_play.src = `./styles/images/img/${index}.jpg`;
      }

      // Update the title
      const title = document.getElementById("title");
      if (title) {
        title.innerHTML = song.songName;
      }

      // Update download link
      const download_music = document.getElementById("download_music");
      if (download_music) {
        download_music.href = `./audio/${index}.mp3`;
        download_music.setAttribute("download", extractSongName(song.songName));
      }

      // Play the song with error handling
      music
        .play()
        .then(() => {
          console.log("Song started playing successfully from detail page");
        })
        .catch((error) => {
          console.log("Auto-play prevented or error:", error);
        });

      // Update UI
      const masterPlay = document.getElementById("masterPlay");
      const wave = document.querySelector(".wave");

      if (masterPlay) {
        masterPlay.classList.remove("bi-play-fill");
        masterPlay.classList.add("bi-pause-fill");
      }

      // Add wave animation
      if (wave) {
        wave.classList.add("active2");
      }

      // Make all play buttons to play icon
      if (typeof makeAllPlays === "function") {
        makeAllPlays();
      }

      // Save current player state
      if (typeof saveCurrentPlayerState === "function") {
        saveCurrentPlayerState(song.id, 0, true);
      }

      // Add to recently played history
      if (typeof addSongToRecentlyPlayed === "function") {
        addSongToRecentlyPlayed(song.id);
      }

      // Update master heart icon
      if (typeof updateMasterHeartIcon === "function") {
        updateMasterHeartIcon();
      }

      // Update the play button for current song
      const playButton = document.getElementById(`${index}`);
      if (playButton) {
        playButton.classList.remove("bi-play-circle-fill");
        playButton.classList.add("bi-pause-circle-fill");
      }

      // Update background
      if (typeof makeAllBackgrounds === "function") {
        makeAllBackgrounds();
      }

      const songItems = document.getElementsByClassName("songItem");
      if (songItems && songItems[index - 1]) {
        songItems[index - 1].style.background = "rgb(105, 105, 170, .1)";
      }
    }
  };

  // Add click event to play buttons
  detailPlayBtn.addEventListener("click", playCurrentSong);
  playBtn.addEventListener("click", playCurrentSong);

  // Heart button elements
  const detailHeartBtn = document.getElementById("detail-heart-btn");
  const detailHeartIcon = document.getElementById("detail-heart-icon");

  // Check if song is already in library and update heart button
  if (isSongInLibrary(song.id)) {
    // Update heart button
    detailHeartIcon.classList.remove("bi-heart");
    detailHeartIcon.classList.add("bi-heart-fill");
    detailHeartBtn.innerHTML = '<i class="bi bi-heart-fill"></i> Đã yêu thích';
    detailHeartBtn.classList.add("in-library");
  }

  // Global function to update detail page heart icon (called from index.js)
  window.updateDetailPageHeartIcon = function (songId) {
    if (song.id === songId) {
      if (isSongInLibrary(songId)) {
        detailHeartIcon.classList.remove("bi-heart");
        detailHeartIcon.classList.add("bi-heart-fill");
        detailHeartBtn.innerHTML =
          '<i class="bi bi-heart-fill"></i> Đã yêu thích';
        detailHeartBtn.classList.add("in-library");

        // Add animation
        detailHeartIcon.classList.add("heart-animation");
        setTimeout(() => {
          detailHeartIcon.classList.remove("heart-animation");
        }, 600);
      } else {
        detailHeartIcon.classList.remove("bi-heart-fill");
        detailHeartIcon.classList.add("bi-heart");
        detailHeartBtn.innerHTML = '<i class="bi bi-heart"></i> Yêu thích';
        detailHeartBtn.classList.remove("in-library");
      }
    }
  };

  // Heart button click handler
  detailHeartBtn.addEventListener("click", () => {
    // Check if user is logged in
    if (typeof isUserLoggedIn === "function" && !isUserLoggedIn()) {
      if (typeof showLoginRequiredNotification === "function") {
        showLoginRequiredNotification();
      }
      return;
    }

    if (isSongInLibrary(song.id)) {
      // Remove from library
      if (removeSongFromLibrary(song.id)) {
        // Update heart button
        detailHeartIcon.classList.remove("bi-heart-fill");
        detailHeartIcon.classList.add("bi-heart");
        detailHeartBtn.innerHTML = '<i class="bi bi-heart"></i> Yêu thích';
        detailHeartBtn.classList.remove("in-library");

        // Show success message
        if (typeof showNotification === "function") {
          showNotification("Đã xóa bài hát khỏi thư viện của bạn!", 3000);
        }

        // Sync all heart icons
        if (typeof syncAllHeartIcons === "function") {
          syncAllHeartIcons(song.id);
        }
      }
    } else {
      // Add to library
      if (addSongToLibrary(song.id)) {
        // Update heart button
        detailHeartIcon.classList.remove("bi-heart");
        detailHeartIcon.classList.add("bi-heart-fill");
        detailHeartBtn.innerHTML =
          '<i class="bi bi-heart-fill"></i> Đã yêu thích';
        detailHeartBtn.classList.add("in-library");

        // Show success message
        if (typeof showNotification === "function") {
          showNotification("Thêm vào thư viện thành công", 3000);
        }

        // Sync all heart icons
        if (typeof syncAllHeartIcons === "function") {
          syncAllHeartIcons(song.id);
        }
      }
    }
  });

  // Add event listener to back button
  const backButton = document.querySelector(".back-button a");
  backButton.addEventListener("click", (e) => {
    e.preventDefault(); // Prevent default navigation

    // Navigate back to the appropriate source page
    // This preserves the current playback state
    const sourceConfig = {
      library: "library.html",
      "recently-played": "recently-played.html",
      discovery: "index.html",
      search: "index.html",
    };

    const backUrl = sourceConfig[source] || "index.html";
    window.location.href = backUrl;
  });
}
